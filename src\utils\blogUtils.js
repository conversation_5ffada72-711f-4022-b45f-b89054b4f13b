import { format, parseISO } from 'date-fns';

/**
 * Utility functions for blog data transformation and formatting
 */

/**
 * Transform database blog entry to display format for existing components
 * @param {Object} blogEntry - Blog entry from database
 * @returns {Object} - Transformed blog entry for display
 */
export const transformBlogEntryForDisplay = (blogEntry) => {
  if (!blogEntry) return null;

  return {
    id: blogEntry.id,
    title: blogEntry.title,
    excerpt: blogEntry.description,
    imageUrl: blogEntry.featured_image_url,
    author: blogEntry.author_name || 'UrbanEdge Team',
    date: blogEntry.publication_date || blogEntry.created_at,
    category: blogEntry.category || 'Real Estate',
    featured: blogEntry.is_featured || false,
    mediumUrl: blogEntry.medium_url,
    status: blogEntry.status
  };
};

/**
 * Transform multiple blog entries for display
 * @param {Array} blogEntries - Array of blog entries from database
 * @returns {Array} - Array of transformed blog entries
 */
export const transformBlogEntriesForDisplay = (blogEntries) => {
  if (!Array.isArray(blogEntries)) return [];
  return blogEntries.map(transformBlogEntryForDisplay).filter(Boolean);
};

/**
 * Format date for display
 * @param {string} dateString - ISO date string
 * @param {string} formatString - Date format string (default: 'MMM dd, yyyy')
 * @returns {string} - Formatted date string
 */
export const formatBlogDate = (dateString, formatString = 'MMM dd, yyyy') => {
  if (!dateString) return '';
  
  try {
    const date = typeof dateString === 'string' ? parseISO(dateString) : dateString;
    return format(date, formatString);
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
};

/**
 * Get blog entry status color for UI display
 * @param {string} status - Blog entry status
 * @returns {string} - Color class or color name
 */
export const getBlogStatusColor = (status) => {
  switch (status) {
    case 'published':
      return 'green';
    case 'draft':
      return 'orange';
    default:
      return 'gray';
  }
};

/**
 * Get blog entry status label
 * @param {string} status - Blog entry status
 * @returns {string} - Human-readable status label
 */
export const getBlogStatusLabel = (status) => {
  switch (status) {
    case 'published':
      return 'Published';
    case 'draft':
      return 'Draft';
    default:
      return 'Unknown';
  }
};

/**
 * Validate blog form data
 * @param {Object} formData - Blog form data
 * @returns {Object} - Validation result with isValid and errors
 */
export const validateBlogFormData = (formData) => {
  const errors = {};

  // Required fields validation
  if (!formData.title?.trim()) {
    errors.title = 'Title is required';
  }

  if (!formData.description?.trim()) {
    errors.description = 'Description is required';
  }

  if (!formData.medium_url?.trim()) {
    errors.medium_url = 'Medium URL is required';
  } else if (!isValidUrl(formData.medium_url)) {
    errors.medium_url = 'Please enter a valid URL';
  }

  if (!formData.featured_image_url?.trim()) {
    errors.featured_image_url = 'Featured image URL is required';
  } else if (!isValidUrl(formData.featured_image_url)) {
    errors.featured_image_url = 'Please enter a valid image URL';
  }

  if (!formData.author_name?.trim()) {
    errors.author_name = 'Author name is required';
  }

  if (!formData.category?.trim()) {
    errors.category = 'Category is required';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Check if a string is a valid URL
 * @param {string} string - String to validate
 * @returns {boolean} - True if valid URL
 */
export const isValidUrl = (string) => {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
};

/**
 * Truncate text to specified length
 * @param {string} text - Text to truncate
 * @param {number} maxLength - Maximum length
 * @returns {string} - Truncated text
 */
export const truncateText = (text, maxLength = 150) => {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + '...';
};

/**
 * Generate slug from title
 * @param {string} title - Blog title
 * @returns {string} - URL-friendly slug
 */
export const generateSlug = (title) => {
  if (!title) return '';
  
  return title
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

/**
 * Get default form data for new blog entry
 * @returns {Object} - Default form data
 */
export const getDefaultBlogFormData = () => ({
  title: '',
  description: '',
  medium_url: '',
  featured_image_url: '',
  author_name: '',
  category: '',
  status: 'draft',
  is_featured: false,
  publication_date: new Date().toISOString().split('T')[0] // Today's date in YYYY-MM-DD format
});

/**
 * Filter blog entries by search term
 * @param {Array} blogEntries - Array of blog entries
 * @param {string} searchTerm - Search term
 * @returns {Array} - Filtered blog entries
 */
export const filterBlogEntriesBySearch = (blogEntries, searchTerm) => {
  if (!searchTerm || !Array.isArray(blogEntries)) return blogEntries;
  
  const term = searchTerm.toLowerCase();
  
  return blogEntries.filter(entry => 
    entry.title?.toLowerCase().includes(term) ||
    entry.description?.toLowerCase().includes(term) ||
    entry.author_name?.toLowerCase().includes(term) ||
    entry.category?.toLowerCase().includes(term)
  );
};

/**
 * Sort blog entries by specified field
 * @param {Array} blogEntries - Array of blog entries
 * @param {string} sortBy - Field to sort by
 * @param {string} sortOrder - Sort order ('asc' or 'desc')
 * @returns {Array} - Sorted blog entries
 */
export const sortBlogEntries = (blogEntries, sortBy = 'created_at', sortOrder = 'desc') => {
  if (!Array.isArray(blogEntries)) return [];
  
  return [...blogEntries].sort((a, b) => {
    let aValue = a[sortBy];
    let bValue = b[sortBy];
    
    // Handle date fields
    if (sortBy.includes('date') || sortBy.includes('_at')) {
      aValue = new Date(aValue);
      bValue = new Date(bValue);
    }
    
    // Handle string fields
    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }
    
    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });
};

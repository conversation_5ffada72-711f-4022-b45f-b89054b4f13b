import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { blogService } from '../lib/blogService';

/**
 * React Query hooks for blog management
 */

// Query keys
export const blogKeys = {
  all: ['blog'],
  lists: () => [...blogKeys.all, 'list'],
  list: (filters, pagination) => [...blogKeys.lists(), { filters, pagination }],
  details: () => [...blogKeys.all, 'detail'],
  detail: (id) => [...blogKeys.details(), id],
  categories: () => [...blogKeys.all, 'categories'],
  featured: () => [...blogKeys.all, 'featured'],
};

/**
 * Hook to fetch all blog entries with optional filtering and pagination
 */
export const useBlogEntries = (filters = {}, pagination = {}, options = {}) => {
  return useQuery({
    queryKey: blogKeys.list(filters, pagination),
    queryFn: () => blogService.getAllBlogEntries(filters, pagination),
    ...options,
  });
};

/**
 * Hook to fetch published blog entries for public display
 */
export const usePublishedBlogEntries = (pagination = {}, options = {}) => {
  return useQuery({
    queryKey: blogKeys.list({ status: 'published' }, pagination),
    queryFn: () => blogService.getPublishedBlogEntries(pagination),
    ...options,
  });
};

/**
 * Hook to fetch a single blog entry by ID
 */
export const useBlogEntry = (id, options = {}) => {
  return useQuery({
    queryKey: blogKeys.detail(id),
    queryFn: () => blogService.getBlogEntryById(id),
    enabled: !!id,
    ...options,
  });
};

/**
 * Hook to fetch featured blog entry
 */
export const useFeaturedBlogEntry = (options = {}) => {
  return useQuery({
    queryKey: blogKeys.featured(),
    queryFn: () => blogService.getFeaturedBlogEntry(),
    ...options,
  });
};

/**
 * Hook to fetch all categories
 */
export const useBlogCategories = (options = {}) => {
  return useQuery({
    queryKey: blogKeys.categories(),
    queryFn: () => blogService.getCategories(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    ...options,
  });
};

/**
 * Hook to create a new blog entry
 */
export const useCreateBlogEntry = (options = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (blogData) => blogService.createBlogEntry(blogData),
    onSuccess: (data) => {
      // Invalidate and refetch blog entries
      queryClient.invalidateQueries({ queryKey: blogKeys.lists() });
      queryClient.invalidateQueries({ queryKey: blogKeys.categories() });
      
      // Add the new entry to the cache
      queryClient.setQueryData(blogKeys.detail(data.id), data);
      
      options.onSuccess?.(data);
    },
    onError: (error) => {
      console.error('Error creating blog entry:', error);
      options.onError?.(error);
    },
    ...options,
  });
};

/**
 * Hook to update an existing blog entry
 */
export const useUpdateBlogEntry = (options = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, updateData }) => blogService.updateBlogEntry(id, updateData),
    onSuccess: (data) => {
      // Update the specific entry in cache
      queryClient.setQueryData(blogKeys.detail(data.id), data);
      
      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: blogKeys.lists() });
      queryClient.invalidateQueries({ queryKey: blogKeys.categories() });
      queryClient.invalidateQueries({ queryKey: blogKeys.featured() });
      
      options.onSuccess?.(data);
    },
    onError: (error) => {
      console.error('Error updating blog entry:', error);
      options.onError?.(error);
    },
    ...options,
  });
};

/**
 * Hook to delete a blog entry
 */
export const useDeleteBlogEntry = (options = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id) => blogService.deleteBlogEntry(id),
    onSuccess: (_, id) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: blogKeys.detail(id) });
      
      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: blogKeys.lists() });
      queryClient.invalidateQueries({ queryKey: blogKeys.featured() });
      
      options.onSuccess?.(_, id);
    },
    onError: (error) => {
      console.error('Error deleting blog entry:', error);
      options.onError?.(error);
    },
    ...options,
  });
};

/**
 * Hook to publish a blog entry
 */
export const usePublishBlogEntry = (options = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id) => blogService.publishBlogEntry(id),
    onSuccess: (data) => {
      // Update the specific entry in cache
      queryClient.setQueryData(blogKeys.detail(data.id), data);
      
      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: blogKeys.lists() });
      queryClient.invalidateQueries({ queryKey: blogKeys.featured() });
      
      options.onSuccess?.(data);
    },
    onError: (error) => {
      console.error('Error publishing blog entry:', error);
      options.onError?.(error);
    },
    ...options,
  });
};

/**
 * Hook to unpublish a blog entry
 */
export const useUnpublishBlogEntry = (options = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id) => blogService.unpublishBlogEntry(id),
    onSuccess: (data) => {
      // Update the specific entry in cache
      queryClient.setQueryData(blogKeys.detail(data.id), data);
      
      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: blogKeys.lists() });
      queryClient.invalidateQueries({ queryKey: blogKeys.featured() });
      
      options.onSuccess?.(data);
    },
    onError: (error) => {
      console.error('Error unpublishing blog entry:', error);
      options.onError?.(error);
    },
    ...options,
  });
};

/**
 * Hook to toggle featured status of a blog entry
 */
export const useToggleFeaturedBlogEntry = (options = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, isFeatured }) => blogService.toggleFeaturedStatus(id, isFeatured),
    onSuccess: (data) => {
      // Update the specific entry in cache
      queryClient.setQueryData(blogKeys.detail(data.id), data);
      
      // Invalidate lists and featured query
      queryClient.invalidateQueries({ queryKey: blogKeys.lists() });
      queryClient.invalidateQueries({ queryKey: blogKeys.featured() });
      
      options.onSuccess?.(data);
    },
    onError: (error) => {
      console.error('Error toggling featured status:', error);
      options.onError?.(error);
    },
    ...options,
  });
};

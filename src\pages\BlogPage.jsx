import { useState, useEffect } from "react";
import { Helmet } from "react-helmet";
import HeroSection from "../components/Blog/HeroSection";
import CategoryFilter from "../components/Blog/CategoryFilter";
import FeaturedPost from "../components/Blog/FeaturedPost";
import BlogGrid from "../components/Blog/BlogGrid";
import Pagination from "../components/Blog/Pagination";
import {
  usePublishedBlogEntries,
  useFeaturedBlogEntry,
  useBlogCategories,
} from "../hooks/useBlog";
import {
  transformBlogEntriesForDisplay,
  transformBlogEntryForDisplay,
} from "../utils/blogUtils";

const BlogPage = () => {
  const [activeCategory, setActiveCategory] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [filteredPosts, setFilteredPosts] = useState([]);
  const postsPerPage = 6;

  // Build filters for the API call
  const filters = {};
  if (activeCategory) {
    filters.category = activeCategory;
  }
  if (searchQuery) {
    filters.search = searchQuery;
  }

  // Fetch published blog entries
  const {
    data: blogData,
    isLoading: isLoadingPosts,
    error: postsError,
  } = usePublishedBlogEntries(filters, {
    page: currentPage,
    limit: postsPerPage,
    orderBy: "publication_date",
    orderDirection: "desc",
  });

  // Fetch featured blog entry
  const {
    data: featuredBlogEntry,
    isLoading: isLoadingFeatured,
  } = useFeaturedBlogEntry();

  // Fetch categories
  const { data: categories = [] } = useBlogCategories();

  // Transform data for display
  const blogPosts = blogData?.data ? transformBlogEntriesForDisplay(blogData.data) : [];
  const featuredPost = featuredBlogEntry ? transformBlogEntryForDisplay(featuredBlogEntry) : null;
  const totalPages = blogData?.totalPages || 1;
  const loading = isLoadingPosts;

  // For client-side filtering when using search (since API search might be limited)
  useEffect(() => {
    let filtered = blogPosts;

    // Remove featured post from regular list if it's being shown separately
    if (featuredPost && !activeCategory && !searchQuery) {
      filtered = filtered.filter((post) => post.id !== featuredPost.id);
    }

    setFilteredPosts(filtered);
  }, [blogPosts, featuredPost, activeCategory, searchQuery]);

  // Get current posts for pagination (when using client-side pagination)
  const indexOfLastPost = currentPage * postsPerPage;
  const indexOfFirstPost = indexOfLastPost - postsPerPage;
  const currentPosts = searchQuery ? filteredPosts.slice(indexOfFirstPost, indexOfLastPost) : filteredPosts;
  const clientSideTotalPages = searchQuery ? Math.ceil(filteredPosts.length / postsPerPage) : totalPages;

  const handleCategoryChange = (category) => {
    setActiveCategory(category);
    setCurrentPage(1); // Reset to first page when category changes
  };

  const handleSearch = (query) => {
    setSearchQuery(query);
    setCurrentPage(1); // Reset to first page when search changes
  };

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
    // Scroll to top of posts section
    const postsSection = document.getElementById("posts-section");
    if (postsSection) {
      window.scrollTo({
        top: postsSection.offsetTop - 100,
        behavior: "smooth",
      });
    }
  };

  // Show error state
  if (postsError) {
    return (
      <>
        <Helmet>
          <title>Blog | UrbanEdge Real Estate</title>
          <meta
            name="description"
            content="Explore real estate insights, market trends, and expert advice on buying, selling, and investing in properties. Stay informed with UrbanEdge's real estate blog."
          />
        </Helmet>

        <HeroSection onSearch={handleSearch} />

        <section className="py-16 bg-beige-light dark:bg-brown">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-red-600 mb-4">
                Error Loading Blog Posts
              </h2>
              <p className="text-gray-600">
                We're having trouble loading the blog posts. Please try again later.
              </p>
            </div>
          </div>
        </section>
      </>
    );
  }

  return (
    <>
      <Helmet>
        <title>Blog | UrbanEdge Real Estate</title>
        <meta
          name="description"
          content="Explore real estate insights, market trends, and expert advice on buying, selling, and investing in properties. Stay informed with UrbanEdge's real estate blog."
        />
      </Helmet>

      <HeroSection onSearch={handleSearch} />

      <section
        className="py-16 bg-beige-light dark:bg-brown"
        id="posts-section"
      >
        <div className="container mx-auto px-4">
          {/* Category Filter */}
          <CategoryFilter
            categories={categories}
            activeCategory={activeCategory}
            onCategoryChange={handleCategoryChange}
          />

          {/* Featured Post (only show if no filters are applied and not loading) */}
          {featuredPost && !activeCategory && !searchQuery && !isLoadingFeatured && (
            <FeaturedPost post={featuredPost} />
          )}

          {/* Blog Grid */}
          <BlogGrid posts={currentPosts} loading={loading} />

          {/* Pagination */}
          {clientSideTotalPages > 1 && (
            <Pagination
              currentPage={currentPage}
              totalPages={clientSideTotalPages}
              onPageChange={handlePageChange}
            />
          )}

          {/* No posts message */}
          {!loading && currentPosts.length === 0 && (
            <div className="text-center py-12">
              <h3 className="text-xl font-semibold text-gray-600 mb-2">
                No blog posts found
              </h3>
              <p className="text-gray-500">
                {searchQuery || activeCategory
                  ? "Try adjusting your search or filter criteria."
                  : "Check back soon for new content!"}
              </p>
            </div>
          )}
        </div>
      </section>
    </>
  );
};

export default BlogPage;

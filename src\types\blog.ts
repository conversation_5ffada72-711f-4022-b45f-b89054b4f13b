/**
 * TypeScript interfaces and types for the blog management system
 */

export type BlogStatus = 'draft' | 'published';

export interface BlogEntry {
  id: string;
  title: string;
  description: string;
  medium_url: string;
  featured_image_url: string;
  publication_date: string | null;
  status: BlogStatus;
  created_at: string;
  updated_at: string;
  created_by: string | null;
  updated_by: string | null;
  author_name: string | null;
  category: string | null;
  is_featured: boolean;
}

export interface BlogFormData {
  title: string;
  description: string;
  medium_url: string;
  featured_image_url: string;
  publication_date?: string;
  status: BlogStatus;
  author_name: string;
  category: string;
  is_featured: boolean;
}

export interface BlogEntryForDisplay {
  id: string;
  title: string;
  excerpt: string;
  imageUrl: string;
  author: string;
  date: string;
  category: string;
  featured: boolean;
  mediumUrl: string;
}

export interface CreateBlogEntryData {
  title: string;
  description: string;
  medium_url: string;
  featured_image_url: string;
  publication_date?: string;
  status?: BlogStatus;
  author_name: string;
  category: string;
  is_featured?: boolean;
}

export interface UpdateBlogEntryData extends Partial<CreateBlogEntryData> {
  updated_by?: string;
}

export interface BlogFilters {
  status?: BlogStatus;
  category?: string;
  is_featured?: boolean;
  search?: string;
}

export interface BlogPaginationParams {
  page?: number;
  limit?: number;
  orderBy?: 'created_at' | 'updated_at' | 'publication_date' | 'title';
  orderDirection?: 'asc' | 'desc';
}

export interface BlogEntriesResponse {
  data: BlogEntry[];
  count: number;
  page: number;
  totalPages: number;
}

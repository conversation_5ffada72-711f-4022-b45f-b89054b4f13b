import { useState, useEffect } from "react";
import {
  Card,
  CardBody,
  Typography,
  Input,
  Textarea,
  Select,
  Option,
  Switch,
  Button,
  Alert,
} from "@material-tailwind/react";
import { useCreateBlogEntry, useUpdateBlogEntry, useBlogCategories } from "../../../hooks/useBlog";
import {
  validateBlogFormData,
  getDefaultBlogFormData,
  formatBlogDate,
} from "../../../utils/blogUtils";

/**
 * Blog Entry Form component for creating and editing blog entries
 */
const BlogEntryForm = ({ entry, onSuccess, onCancel }) => {
  const isEditing = !!entry;
  const [formData, setFormData] = useState(getDefaultBlogFormData());
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Mutations
  const createMutation = useCreateBlogEntry({
    onSuccess: () => {
      onSuccess();
    },
    onError: (error) => {
      console.error('Create error:', error);
    },
  });

  const updateMutation = useUpdateBlogEntry({
    onSuccess: () => {
      onSuccess();
    },
    onError: (error) => {
      console.error('Update error:', error);
    },
  });

  // Fetch categories for dropdown
  const { data: categories = [] } = useBlogCategories();

  // Initialize form data when editing
  useEffect(() => {
    if (entry) {
      setFormData({
        title: entry.title || '',
        description: entry.description || '',
        medium_url: entry.medium_url || '',
        featured_image_url: entry.featured_image_url || '',
        author_name: entry.author_name || '',
        category: entry.category || '',
        status: entry.status || 'draft',
        is_featured: entry.is_featured || false,
        publication_date: entry.publication_date 
          ? new Date(entry.publication_date).toISOString().split('T')[0]
          : new Date().toISOString().split('T')[0],
      });
    }
  }, [entry]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate form data
    const validation = validateBlogFormData(formData);
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      if (isEditing) {
        await updateMutation.mutateAsync({
          id: entry.id,
          updateData: formData
        });
      } else {
        await createMutation.mutateAsync(formData);
      }
    } catch (error) {
      console.error('Form submission error:', error);
      setErrors({ submit: error.message || 'An error occurred while saving' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const isLoading = createMutation.isPending || updateMutation.isPending || isSubmitting;

  return (
    <Card>
      <CardBody>
        <div className="mb-6">
          <Typography variant="h5" color="blue-gray">
            {isEditing ? 'Edit Blog Entry' : 'Create New Blog Entry'}
          </Typography>
          <Typography color="gray" className="mt-1">
            {isEditing ? 'Update the blog entry details below' : 'Fill in the details to create a new blog entry'}
          </Typography>
        </div>

        {errors.submit && (
          <Alert color="red" className="mb-6">
            {errors.submit}
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Title */}
          <div>
            <Input
              label="Blog Title *"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              error={!!errors.title}
              disabled={isLoading}
            />
            {errors.title && (
              <Typography variant="small" color="red" className="mt-1">
                {errors.title}
              </Typography>
            )}
          </div>

          {/* Description */}
          <div>
            <Textarea
              label="Description/Excerpt *"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              error={!!errors.description}
              disabled={isLoading}
              rows={4}
            />
            {errors.description && (
              <Typography variant="small" color="red" className="mt-1">
                {errors.description}
              </Typography>
            )}
          </div>

          {/* Medium URL */}
          <div>
            <Input
              label="Medium Article URL *"
              value={formData.medium_url}
              onChange={(e) => handleInputChange('medium_url', e.target.value)}
              error={!!errors.medium_url}
              disabled={isLoading}
              placeholder="https://medium.com/@author/article-title"
            />
            {errors.medium_url && (
              <Typography variant="small" color="red" className="mt-1">
                {errors.medium_url}
              </Typography>
            )}
          </div>

          {/* Featured Image URL */}
          <div>
            <Input
              label="Featured Image URL *"
              value={formData.featured_image_url}
              onChange={(e) => handleInputChange('featured_image_url', e.target.value)}
              error={!!errors.featured_image_url}
              disabled={isLoading}
              placeholder="https://example.com/image.jpg"
            />
            {errors.featured_image_url && (
              <Typography variant="small" color="red" className="mt-1">
                {errors.featured_image_url}
              </Typography>
            )}
          </div>

          {/* Author and Category Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Input
                label="Author Name *"
                value={formData.author_name}
                onChange={(e) => handleInputChange('author_name', e.target.value)}
                error={!!errors.author_name}
                disabled={isLoading}
              />
              {errors.author_name && (
                <Typography variant="small" color="red" className="mt-1">
                  {errors.author_name}
                </Typography>
              )}
            </div>

            <div>
              <Select
                label="Category *"
                value={formData.category}
                onChange={(value) => handleInputChange('category', value)}
                error={!!errors.category}
                disabled={isLoading}
              >
                {categories.map((category) => (
                  <Option key={category} value={category}>
                    {category}
                  </Option>
                ))}
                <Option value="Real Estate">Real Estate</Option>
                <Option value="Market Trends">Market Trends</Option>
                <Option value="Investment">Investment</Option>
                <Option value="Home Buying">Home Buying</Option>
                <Option value="Property Management">Property Management</Option>
              </Select>
              {errors.category && (
                <Typography variant="small" color="red" className="mt-1">
                  {errors.category}
                </Typography>
              )}
            </div>
          </div>

          {/* Publication Date and Status Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Input
                type="date"
                label="Publication Date"
                value={formData.publication_date}
                onChange={(e) => handleInputChange('publication_date', e.target.value)}
                disabled={isLoading}
              />
            </div>

            <div>
              <Select
                label="Status"
                value={formData.status}
                onChange={(value) => handleInputChange('status', value)}
                disabled={isLoading}
              >
                <Option value="draft">Draft</Option>
                <Option value="published">Published</Option>
              </Select>
            </div>
          </div>

          {/* Featured Toggle */}
          <div className="flex items-center gap-3">
            <Switch
              checked={formData.is_featured}
              onChange={(e) => handleInputChange('is_featured', e.target.checked)}
              disabled={isLoading}
            />
            <Typography color="blue-gray">
              Mark as featured post
            </Typography>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              color="blue"
              loading={isLoading}
              className="flex-1"
            >
              {isEditing ? 'Update Entry' : 'Create Entry'}
            </Button>
            <Button
              type="button"
              variant="outlined"
              color="gray"
              onClick={onCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
          </div>
        </form>
      </CardBody>
    </Card>
  );
};

export default BlogEntryForm;

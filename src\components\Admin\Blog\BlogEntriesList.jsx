import { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alog<PERSON><PERSON>,
  DialogFooter,
} from "@material-tailwind/react";
import {
  PencilSquareIcon,
  TrashIcon,
  EyeIcon,
  StarIcon,
} from "@heroicons/react/24/outline";
import { StarIcon as StarIconSolid } from "@heroicons/react/24/solid";
import {
  useDeleteBlogEntry,
  usePublishBlogEntry,
  useUnpublishBlogEntry,
  useToggleFeaturedBlogEntry,
} from "../../../hooks/useBlog";
import {
  formatBlogDate,
  getBlogStatusColor,
  getBlogStatusLabel,
  truncateText,
} from "../../../utils/blogUtils";
import BlogEntryCard from "./BlogEntryCard";

/**
 * Blog Entries List component for displaying and managing blog entries
 */
const BlogEntriesList = ({
  blogData,
  isLoading,
  error,
  onEdit,
  onPageC<PERSON>e,
  currentPage,
}) => {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [entryToDelete, setEntryToDelete] = useState(null);

  // Mutations
  const deleteMutation = useDeleteBlogEntry({
    onSuccess: () => {
      setDeleteDialogOpen(false);
      setEntryToDelete(null);
    },
  });

  const publishMutation = usePublishBlogEntry();
  const unpublishMutation = useUnpublishBlogEntry();
  const toggleFeaturedMutation = useToggleFeaturedBlogEntry();

  const handleDelete = (entry) => {
    setEntryToDelete(entry);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (entryToDelete) {
      deleteMutation.mutate(entryToDelete.id);
    }
  };

  const handlePublishToggle = (entry) => {
    if (entry.status === 'published') {
      unpublishMutation.mutate(entry.id);
    } else {
      publishMutation.mutate(entry.id);
    }
  };

  const handleFeaturedToggle = (entry) => {
    toggleFeaturedMutation.mutate({
      id: entry.id,
      isFeatured: !entry.is_featured,
    });
  };

  const handleViewMedium = (entry) => {
    window.open(entry.medium_url, '_blank');
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="text-center">
          <Spinner className="h-8 w-8 mb-4" color="blue" />
          <Typography color="gray">Loading blog entries...</Typography>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert color="red" className="mb-6">
        <Typography variant="h6" className="mb-1">
          Error loading blog entries
        </Typography>
        <Typography variant="small">
          {error.message || 'An unexpected error occurred'}
        </Typography>
      </Alert>
    );
  }

  const { data: entries = [], count = 0, totalPages = 1 } = blogData || {};

  if (entries.length === 0) {
    return (
      <div className="text-center py-12">
        <Typography variant="h6" color="gray" className="mb-2">
          No blog entries found
        </Typography>
        <Typography color="gray">
          Create your first blog entry to get started.
        </Typography>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <Typography color="gray" variant="small">
          Showing {entries.length} of {count} entries
        </Typography>
        <Typography color="gray" variant="small">
          Page {currentPage} of {totalPages}
        </Typography>
      </div>

      {/* Entries Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {entries.map((entry) => (
          <BlogEntryCard
            key={entry.id}
            entry={entry}
            onEdit={() => onEdit(entry)}
            onDelete={() => handleDelete(entry)}
            onPublishToggle={() => handlePublishToggle(entry)}
            onFeaturedToggle={() => handleFeaturedToggle(entry)}
            onViewMedium={() => handleViewMedium(entry)}
            isPublishing={publishMutation.isPending && publishMutation.variables === entry.id}
            isUnpublishing={unpublishMutation.isPending && unpublishMutation.variables === entry.id}
            isTogglingFeatured={
              toggleFeaturedMutation.isPending && 
              toggleFeaturedMutation.variables?.id === entry.id
            }
          />
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center gap-2 mt-8">
          <Button
            variant="outlined"
            size="sm"
            disabled={currentPage === 1}
            onClick={() => onPageChange(currentPage - 1)}
          >
            Previous
          </Button>
          
          <div className="flex gap-1">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <Button
                key={page}
                variant={page === currentPage ? "filled" : "outlined"}
                size="sm"
                onClick={() => onPageChange(page)}
                className="min-w-[40px]"
              >
                {page}
              </Button>
            ))}
          </div>

          <Button
            variant="outlined"
            size="sm"
            disabled={currentPage === totalPages}
            onClick={() => onPageChange(currentPage + 1)}
          >
            Next
          </Button>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} handler={setDeleteDialogOpen}>
        <DialogHeader>Confirm Delete</DialogHeader>
        <DialogBody>
          Are you sure you want to delete "{entryToDelete?.title}"? This action cannot be undone.
        </DialogBody>
        <DialogFooter>
          <Button
            variant="text"
            color="gray"
            onClick={() => setDeleteDialogOpen(false)}
            className="mr-1"
          >
            Cancel
          </Button>
          <Button
            variant="filled"
            color="red"
            onClick={confirmDelete}
            loading={deleteMutation.isPending}
          >
            Delete
          </Button>
        </DialogFooter>
      </Dialog>
    </div>
  );
};

export default BlogEntriesList;

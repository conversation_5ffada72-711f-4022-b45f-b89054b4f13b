import { useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet";
import { useBlogEntry } from "../hooks/useBlog";
import { transformBlogEntryForDisplay } from "../utils/blogUtils";

/**
 * BlogPostPage component that fetches a blog entry and redirects to Medium
 * This page serves as a bridge to provide SEO benefits while redirecting users to the actual Medium article
 */
const BlogPostPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  // Fetch the blog entry
  const {
    data: blogEntry,
    isLoading,
    error,
  } = useBlogEntry(id);

  // Transform data for display
  const post = blogEntry ? transformBlogEntryForDisplay(blogEntry) : null;

  // Redirect to Medium article after a brief delay for SEO
  useEffect(() => {
    if (post?.mediumUrl) {
      // Small delay to allow search engines to index the page
      const timer = setTimeout(() => {
        window.location.href = post.mediumUrl;
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [post]);

  // Loading state
  if (isLoading) {
    return (
      <>
        <Helmet>
          <title>Loading... | UrbanEdge Real Estate Blog</title>
          <meta name="description" content="Loading blog post..." />
        </Helmet>

        <div className="py-12 bg-beige-light dark:bg-brown min-h-screen">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              {/* Loading skeleton */}
              <div className="animate-pulse">
                <div className="h-8 bg-beige-medium dark:bg-brown-dark rounded w-3/4 mb-4"></div>
                <div className="h-4 bg-beige-medium dark:bg-brown-dark rounded w-1/2 mb-8"></div>
                <div className="h-96 bg-beige-medium dark:bg-brown-dark rounded mb-8"></div>
                <div className="space-y-4">
                  <div className="h-4 bg-beige-medium dark:bg-brown-dark rounded w-full"></div>
                  <div className="h-4 bg-beige-medium dark:bg-brown-dark rounded w-full"></div>
                  <div className="h-4 bg-beige-medium dark:bg-brown-dark rounded w-3/4"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  // Error state
  if (error) {
    return (
      <>
        <Helmet>
          <title>Article Not Found | UrbanEdge Real Estate Blog</title>
          <meta name="description" content="The requested blog post could not be found." />
        </Helmet>

        <div className="py-12 bg-beige-light dark:bg-brown min-h-screen">
          <div className="container mx-auto px-4">
            <div className="max-w-2xl mx-auto text-center">
              <h1 className="text-3xl font-bold text-brown-dark dark:text-beige-light mb-4">
                Article Not Found
              </h1>
              <p className="text-brown dark:text-beige-medium mb-8">
                The article you're looking for doesn't exist or has been removed.
              </p>
              <div className="space-x-4">
                <button
                  onClick={() => navigate('/blog')}
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Back to Blog
                </button>
                <button
                  onClick={() => navigate('/')}
                  className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors"
                >
                  Go Home
                </button>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  // Post not found
  if (!post) {
    return (
      <>
        <Helmet>
          <title>Article Not Found | UrbanEdge Real Estate Blog</title>
          <meta name="description" content="The requested blog post could not be found." />
        </Helmet>

        <div className="py-12 bg-beige-light dark:bg-brown min-h-screen">
          <div className="container mx-auto px-4">
            <div className="max-w-2xl mx-auto text-center">
              <h1 className="text-3xl font-bold text-brown-dark dark:text-beige-light mb-4">
                Article Not Found
              </h1>
              <p className="text-brown dark:text-beige-medium mb-8">
                The article you're looking for doesn't exist or has been removed.
              </p>
              <button
                onClick={() => navigate('/blog')}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Back to Blog
              </button>
            </div>
          </div>
        </div>
      </>
    );
  }

  // Main content with redirect notice
  return (
    <>
      <Helmet>
        <title>{post.title} | UrbanEdge Real Estate Blog</title>
        <meta name="description" content={post.excerpt} />
        <meta property="og:title" content={post.title} />
        <meta property="og:description" content={post.excerpt} />
        <meta property="og:image" content={post.imageUrl} />
        <meta property="og:type" content="article" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={post.title} />
        <meta name="twitter:description" content={post.excerpt} />
        <meta name="twitter:image" content={post.imageUrl} />
        <link rel="canonical" href={post.mediumUrl} />
      </Helmet>

      <div className="py-12 bg-beige-light dark:bg-brown min-h-screen">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Redirect Notice */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6 mb-8">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-blue-800 dark:text-blue-200">
                      You're being redirected to read the full article on Medium...
                    </p>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                </div>
              </div>
              <div className="mt-4">
                <a
                  href={post.mediumUrl}
                  className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Click here if you're not redirected automatically →
                </a>
              </div>
            </div>

            {/* Article Preview */}
            <article className="bg-white dark:bg-brown-dark rounded-lg shadow-sm overflow-hidden">
              {/* Featured Image */}
              <div className="aspect-video w-full overflow-hidden">
                <img
                  src={post.imageUrl}
                  alt={post.title}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.target.src = 'https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80';
                  }}
                />
              </div>

              {/* Content */}
              <div className="p-8">
                {/* Category and Date */}
                <div className="flex items-center gap-4 mb-4">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                    {post.category}
                  </span>
                  <time className="text-sm text-gray-500 dark:text-gray-400">
                    {new Date(post.date).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </time>
                </div>

                {/* Title */}
                <h1 className="text-3xl md:text-4xl font-bold text-brown-dark dark:text-beige-light mb-4">
                  {post.title}
                </h1>

                {/* Author */}
                <div className="flex items-center mb-6">
                  <div className="flex-shrink-0">
                    <div className="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {post.author.charAt(0)}
                      </span>
                    </div>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-brown-dark dark:text-beige-light">
                      {post.author}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Real Estate Expert
                    </p>
                  </div>
                </div>

                {/* Excerpt */}
                <div className="prose prose-lg dark:prose-invert max-w-none">
                  <p className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed">
                    {post.excerpt}
                  </p>
                </div>

                {/* Call to Action */}
                <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                  <a
                    href={post.mediumUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                  >
                    Read Full Article on Medium
                    <svg className="ml-2 -mr-1 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </a>
                </div>
              </div>
            </article>
          </div>
        </div>
      </div>
    </>
  );
};

export default BlogPostPage;

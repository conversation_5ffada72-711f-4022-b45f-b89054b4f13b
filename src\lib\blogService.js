import { supabase } from './supabase';

/**
 * Blog service for managing blog entries in Supabase
 */
export const blogService = {
  /**
   * Get all blog entries with optional filtering and pagination
   */
  async getAllBlogEntries(filters = {}, pagination = {}) {
    try {
      const {
        status,
        category,
        is_featured,
        search
      } = filters;

      const {
        page = 1,
        limit = 10,
        orderBy = 'created_at',
        orderDirection = 'desc'
      } = pagination;

      let query = supabase
        .from('blog_entries')
        .select('*', { count: 'exact' });

      // Apply filters
      if (status) {
        query = query.eq('status', status);
      }

      if (category) {
        query = query.eq('category', category);
      }

      if (is_featured !== undefined) {
        query = query.eq('is_featured', is_featured);
      }

      if (search) {
        query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%,author_name.ilike.%${search}%`);
      }

      // Apply ordering
      query = query.order(orderBy, { ascending: orderDirection === 'asc' });

      // Apply pagination
      const from = (page - 1) * limit;
      const to = from + limit - 1;
      query = query.range(from, to);

      const { data, error, count } = await query;

      if (error) throw error;

      return {
        data: data || [],
        count: count || 0,
        page,
        totalPages: Math.ceil((count || 0) / limit)
      };
    } catch (error) {
      console.error('Error fetching blog entries:', error);
      throw error;
    }
  },

  /**
   * Get published blog entries for public display
   */
  async getPublishedBlogEntries(pagination = {}) {
    return this.getAllBlogEntries({ status: 'published' }, pagination);
  },

  /**
   * Get a single blog entry by ID
   */
  async getBlogEntryById(id) {
    try {
      const { data, error } = await supabase
        .from('blog_entries')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching blog entry:', error);
      throw error;
    }
  },

  /**
   * Create a new blog entry
   */
  async createBlogEntry(blogData) {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      const entryData = {
        ...blogData,
        created_by: user?.id,
        updated_by: user?.id,
        status: blogData.status || 'draft',
        is_featured: blogData.is_featured || false,
        publication_date: blogData.publication_date || new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('blog_entries')
        .insert([entryData])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating blog entry:', error);
      throw error;
    }
  },

  /**
   * Update an existing blog entry
   */
  async updateBlogEntry(id, updateData) {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      const entryData = {
        ...updateData,
        updated_by: user?.id,
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('blog_entries')
        .update(entryData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating blog entry:', error);
      throw error;
    }
  },

  /**
   * Delete a blog entry
   */
  async deleteBlogEntry(id) {
    try {
      const { error } = await supabase
        .from('blog_entries')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error deleting blog entry:', error);
      throw error;
    }
  },

  /**
   * Publish a blog entry
   */
  async publishBlogEntry(id) {
    return this.updateBlogEntry(id, { 
      status: 'published',
      publication_date: new Date().toISOString()
    });
  },

  /**
   * Unpublish a blog entry (set to draft)
   */
  async unpublishBlogEntry(id) {
    return this.updateBlogEntry(id, { status: 'draft' });
  },

  /**
   * Toggle featured status of a blog entry
   */
  async toggleFeaturedStatus(id, isFeatured) {
    return this.updateBlogEntry(id, { is_featured: isFeatured });
  },

  /**
   * Get all unique categories
   */
  async getCategories() {
    try {
      const { data, error } = await supabase
        .from('blog_entries')
        .select('category')
        .not('category', 'is', null)
        .neq('category', '');

      if (error) throw error;

      // Extract unique categories
      const categories = [...new Set(data.map(item => item.category))].filter(Boolean);
      return categories.sort();
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }
  },

  /**
   * Get featured blog entry
   */
  async getFeaturedBlogEntry() {
    try {
      const { data, error } = await supabase
        .from('blog_entries')
        .select('*')
        .eq('status', 'published')
        .eq('is_featured', true)
        .order('publication_date', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') throw error; // PGRST116 is "no rows returned"
      return data;
    } catch (error) {
      console.error('Error fetching featured blog entry:', error);
      throw error;
    }
  }
};

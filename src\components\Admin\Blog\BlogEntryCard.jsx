import {
  <PERSON>,
  CardB<PERSON>,
  CardH<PERSON>er,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  IconButton,
  <PERSON>lt<PERSON>,
} from "@material-tailwind/react";
import {
  PencilSquareIcon,
  TrashIcon,
  EyeIcon,
  StarIcon,
  GlobeAltIcon,
} from "@heroicons/react/24/outline";
import { StarIcon as StarIconSolid } from "@heroicons/react/24/solid";
import {
  formatBlogDate,
  getBlogStatusColor,
  getBlogStatusLabel,
  truncateText,
} from "../../../utils/blogUtils";

/**
 * Blog Entry Card component for displaying individual blog entries in admin
 */
const BlogEntryCard = ({
  entry,
  onEdit,
  onDelete,
  onPublishToggle,
  onFeaturedToggle,
  onViewMedium,
  isPublishing,
  isUnpublishing,
  isTogglingFeatured,
}) => {
  const isPublished = entry.status === 'published';
  const isFeatured = entry.is_featured;

  return (
    <Card className="h-full">
      <CardHeader floated={false} className="h-48 relative">
        <img
          src={entry.featured_image_url}
          alt={entry.title}
          className="h-full w-full object-cover"
          onError={(e) => {
            e.target.src = 'https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80';
          }}
        />
        
        {/* Status and Featured Badges */}
        <div className="absolute top-2 left-2 flex gap-2">
          <Chip
            size="sm"
            color={getBlogStatusColor(entry.status)}
            value={getBlogStatusLabel(entry.status)}
            className="text-xs"
          />
          {isFeatured && (
            <Chip
              size="sm"
              color="amber"
              value="Featured"
              className="text-xs"
              icon={<StarIconSolid className="h-3 w-3" />}
            />
          )}
        </div>

        {/* Action Buttons */}
        <div className="absolute top-2 right-2 flex gap-1">
          <Tooltip content="View on Medium">
            <IconButton
              size="sm"
              color="blue"
              variant="filled"
              onClick={onViewMedium}
              className="bg-blue-500/80 hover:bg-blue-500"
            >
              <GlobeAltIcon className="h-4 w-4" />
            </IconButton>
          </Tooltip>
        </div>
      </CardHeader>

      <CardBody className="flex flex-col h-full">
        {/* Title and Category */}
        <div className="flex-1">
          <div className="flex items-start justify-between mb-2">
            <Typography variant="h6" color="blue-gray" className="flex-1 line-clamp-2">
              {entry.title}
            </Typography>
          </div>

          {/* Category and Author */}
          <div className="flex items-center gap-2 mb-3">
            {entry.category && (
              <Chip
                size="sm"
                variant="outlined"
                value={entry.category}
                className="text-xs"
              />
            )}
            <Typography variant="small" color="gray">
              by {entry.author_name || 'Unknown'}
            </Typography>
          </div>

          {/* Description */}
          <Typography variant="small" color="gray" className="mb-4 line-clamp-3">
            {truncateText(entry.description, 120)}
          </Typography>

          {/* Dates */}
          <div className="space-y-1 mb-4">
            <Typography variant="small" color="gray">
              Created: {formatBlogDate(entry.created_at)}
            </Typography>
            {entry.publication_date && (
              <Typography variant="small" color="gray">
                Published: {formatBlogDate(entry.publication_date)}
              </Typography>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col gap-2 mt-auto">
          {/* Primary Actions */}
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outlined"
              color="blue"
              onClick={onEdit}
              className="flex-1 flex items-center justify-center gap-1"
            >
              <PencilSquareIcon className="h-4 w-4" />
              Edit
            </Button>
            
            <Button
              size="sm"
              variant="outlined"
              color="red"
              onClick={onDelete}
              className="flex items-center justify-center gap-1"
            >
              <TrashIcon className="h-4 w-4" />
              Delete
            </Button>
          </div>

          {/* Secondary Actions */}
          <div className="flex gap-2">
            <Button
              size="sm"
              variant={isPublished ? "outlined" : "filled"}
              color={isPublished ? "orange" : "green"}
              onClick={onPublishToggle}
              loading={isPublishing || isUnpublishing}
              className="flex-1"
            >
              {isPublished ? 'Unpublish' : 'Publish'}
            </Button>

            <Tooltip content={isFeatured ? "Remove from featured" : "Mark as featured"}>
              <IconButton
                size="sm"
                variant={isFeatured ? "filled" : "outlined"}
                color="amber"
                onClick={onFeaturedToggle}
                loading={isTogglingFeatured}
              >
                {isFeatured ? (
                  <StarIconSolid className="h-4 w-4" />
                ) : (
                  <StarIcon className="h-4 w-4" />
                )}
              </IconButton>
            </Tooltip>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default BlogEntryCard;

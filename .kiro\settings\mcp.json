{"mcpServers": {"supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest"], "env": {"SUPABASE_ACCESS_TOKEN": "********************************************"}, "disabled": false, "autoApprove": []}, "Sequential Thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {}, "disabled": false, "autoApprove": ["sequentialthinking"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {"DEFAULT_MINIMUM_TOKENS": "10000"}, "disabled": false, "autoApprove": []}, "Playwright": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"], "env": {}, "disabled": false, "autoApprove": []}, "Memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"MEMORY_FILE_PATH": "memory.json"}, "disabled": false, "autoApprove": []}}}
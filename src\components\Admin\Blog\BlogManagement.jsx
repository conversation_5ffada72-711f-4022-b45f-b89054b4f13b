import { useState } from "react";
import {
  <PERSON>,
  Card<PERSON>ody,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Tabs,
  TabsHeader,
  TabsBody,
  Tab,
  TabPanel,
  Input,
  Select,
  Option,
} from "@material-tailwind/react";
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
} from "@heroicons/react/24/outline";
import { useBlogEntries, useBlogCategories } from "../../../hooks/useBlog";
import BlogEntriesList from "./BlogEntriesList";
import BlogEntryForm from "./BlogEntryForm";

/**
 * Blog Management component for admin dashboard
 * Provides CRUD operations for blog entries
 */
const BlogManagement = () => {
  const [activeTab, setActiveTab] = useState("list");
  const [editingEntry, setEditingEntry] = useState(null);
  const [filters, setFilters] = useState({
    search: "",
    status: "",
    category: "",
    is_featured: "",
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    orderBy: "created_at",
    orderDirection: "desc",
  });

  // Fetch blog entries with current filters and pagination
  const {
    data: blogData,
    isLoading: isLoadingEntries,
    error: entriesError,
    refetch: refetchEntries,
  } = useBlogEntries(filters, pagination);

  // Fetch categories for filter dropdown
  const { data: categories = [] } = useBlogCategories();

  const handleCreateNew = () => {
    setEditingEntry(null);
    setActiveTab("form");
  };

  const handleEdit = (entry) => {
    setEditingEntry(entry);
    setActiveTab("form");
  };

  const handleFormSuccess = () => {
    setActiveTab("list");
    setEditingEntry(null);
    refetchEntries();
  };

  const handleFormCancel = () => {
    setActiveTab("list");
    setEditingEntry(null);
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
    setPagination(prev => ({
      ...prev,
      page: 1 // Reset to first page when filtering
    }));
  };

  const handleClearFilters = () => {
    setFilters({
      search: "",
      status: "",
      category: "",
      is_featured: "",
    });
    setPagination(prev => ({
      ...prev,
      page: 1
    }));
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({
      ...prev,
      page: newPage
    }));
  };

  const tabs = [
    {
      label: "Blog Entries",
      value: "list",
      icon: <FunnelIcon className="h-5 w-5" />,
    },
    {
      label: editingEntry ? "Edit Entry" : "New Entry",
      value: "form",
      icon: <PlusIcon className="h-5 w-5" />,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Typography variant="h4" color="blue-gray">
            Blog Management
          </Typography>
          <Typography color="gray" className="mt-1">
            Manage blog entries, create new posts, and control publication status
          </Typography>
        </div>
        <Button
          onClick={handleCreateNew}
          className="flex items-center gap-2"
          color="blue"
        >
          <PlusIcon className="h-5 w-5" />
          New Blog Entry
        </Button>
      </div>

      {/* Tabs */}
      <Card>
        <CardBody>
          <Tabs value={activeTab} onChange={setActiveTab}>
            <TabsHeader>
              {tabs.map(({ label, value, icon }) => (
                <Tab key={value} value={value}>
                  <div className="flex items-center gap-2">
                    {icon}
                    {label}
                  </div>
                </Tab>
              ))}
            </TabsHeader>

            <TabsBody>
              <TabPanel value="list" className="p-0 pt-6">
                {/* Filters */}
                <div className="mb-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="relative">
                    <Input
                      label="Search"
                      icon={<MagnifyingGlassIcon className="h-5 w-5" />}
                      value={filters.search}
                      onChange={(e) => handleFilterChange("search", e.target.value)}
                      placeholder="Search by title, description, or author..."
                    />
                  </div>

                  <Select
                    label="Status"
                    value={filters.status}
                    onChange={(value) => handleFilterChange("status", value)}
                  >
                    <Option value="">All Statuses</Option>
                    <Option value="published">Published</Option>
                    <Option value="draft">Draft</Option>
                  </Select>

                  <Select
                    label="Category"
                    value={filters.category}
                    onChange={(value) => handleFilterChange("category", value)}
                  >
                    <Option value="">All Categories</Option>
                    {categories.map((category) => (
                      <Option key={category} value={category}>
                        {category}
                      </Option>
                    ))}
                  </Select>

                  <Select
                    label="Featured"
                    value={filters.is_featured}
                    onChange={(value) => handleFilterChange("is_featured", value)}
                  >
                    <Option value="">All</Option>
                    <Option value="true">Featured Only</Option>
                    <Option value="false">Not Featured</Option>
                  </Select>
                </div>

                {/* Clear Filters Button */}
                {(filters.search || filters.status || filters.category || filters.is_featured) && (
                  <div className="mb-4">
                    <Button
                      variant="outlined"
                      size="sm"
                      onClick={handleClearFilters}
                    >
                      Clear Filters
                    </Button>
                  </div>
                )}

                {/* Blog Entries List */}
                <BlogEntriesList
                  blogData={blogData}
                  isLoading={isLoadingEntries}
                  error={entriesError}
                  onEdit={handleEdit}
                  onPageChange={handlePageChange}
                  currentPage={pagination.page}
                />
              </TabPanel>

              <TabPanel value="form" className="p-0 pt-6">
                <BlogEntryForm
                  entry={editingEntry}
                  onSuccess={handleFormSuccess}
                  onCancel={handleFormCancel}
                />
              </TabPanel>
            </TabsBody>
          </Tabs>
        </CardBody>
      </Card>
    </div>
  );
};

export default BlogManagement;
